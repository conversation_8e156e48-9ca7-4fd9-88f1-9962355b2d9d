"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import {
  X,
  Settings,
  LogOut,
  Plus,
  Menu,
  Stethoscope,
} from "lucide-react"
import ConversationHistory from "@/components/conversation-history"

interface ConversationSidebarProps {
  currentUser: any
  onLogout: () => void
  onNewConversation: () => void
  onConversationSelect: (id: string) => void
  onConversationDeleted: (id: string) => void
  activeConversation?: string | null
  isOpen?: boolean
  onToggle?: (open: boolean) => void
  showCloseButton?: boolean
  showSettings?: boolean
  className?: string
  newButtonText?: string
  newButtonIcon?: React.ReactNode
}

export default function ConversationSidebar({
  currentUser,
  onLogout,
  onNewConversation,
  onConversationSelect,
  onConversationDeleted,
  activeConversation = null,
  isOpen = true,
  onToggle,
  showCloseButton = false,
  showSettings = false,
  className = "",
  newButtonText = "New Consultation",
  newButtonIcon = <Plus className="h-4 w-4 mr-2" />
}: ConversationSidebarProps) {
  const [internalOpen, setInternalOpen] = useState(isOpen)
  
  // Use external state if provided, otherwise use internal state
  const sidebarOpen = onToggle ? isOpen : internalOpen
  const setSidebarOpen = onToggle || setInternalOpen

  const handleToggle = (open: boolean) => {
    setSidebarOpen(open)
  }

  return (
    <>
      {/* Sidebar */}
      <div
        className={`${
          sidebarOpen ? "w-80" : "w-0"
        } transition-all duration-300 overflow-hidden border-r border-gray-200 flex flex-col flex-shrink-0 sidebar-stable ${className}`}
        style={{ minWidth: sidebarOpen ? '320px' : '0px' }}
      >
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Stethoscope className="h-5 w-5 text-white" />
              </div>
              <span className="font-semibold text-gray-900">Medical AI</span>
            </div>
            {showCloseButton && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleToggle(false)} 
                className="md:hidden"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* New Chat Button */}
          <Button 
            onClick={onNewConversation} 
            className="w-full justify-start bg-blue-600 hover:bg-blue-700 text-white"
          >
            {newButtonIcon}
            {newButtonText}
          </Button>
        </div>

        {/* Conversation History Component */}
        <div className="flex-1 overflow-hidden">
          <ConversationHistory
            onConversationSelect={onConversationSelect}
            activeConversation={activeConversation}
            onConversationDeleted={onConversationDeleted}
          />
        </div>

        {/* User Profile */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {currentUser?.email?.charAt(0).toUpperCase() || "U"}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {currentUser?.email || "User"}
                </p>
                <p className="text-xs text-gray-500">Patient</p>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              {showSettings && (
                <Button variant="ghost" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              )}
              <Button variant="ghost" size="sm" onClick={onLogout}>
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Menu button for when sidebar is closed - only show if onToggle is provided */}
      {!sidebarOpen && onToggle && (
        <div className="fixed top-4 left-4 z-50">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggle(true)}
            className="bg-white shadow-md border"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>
      )}
    </>
  )
}
