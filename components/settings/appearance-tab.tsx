"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Sun, Moon, Monitor, CheckCircle } from "lucide-react"
import { toast } from "sonner"
import { UserProfileAPI } from "@/lib/user-profile-api"

type Theme = "light" | "dark" | "system"

interface ThemeOption {
  value: Theme
  label: string
  description: string
  icon: React.ReactNode
}

const themeOptions: ThemeOption[] = [
  {
    value: "light",
    label: "Light",
    description: "Clean and bright interface",
    icon: <Sun className="h-5 w-5" />
  },
  {
    value: "dark",
    label: "Dark",
    description: "Easy on the eyes in low light",
    icon: <Moon className="h-5 w-5" />
  },
  {
    value: "system",
    label: "System",
    description: "Follows your device settings",
    icon: <Monitor className="h-5 w-5" />
  }
]

export default function AppearanceTab() {
  const [currentTheme, setCurrentTheme] = useState<Theme>("light")
  const [isApplying, setIsApplying] = useState(false)

  // Load current theme from localStorage on component mount
  useEffect(() => {
    const savedTheme = localStorage.getItem("theme") as Theme
    if (savedTheme && ["light", "dark", "system"].includes(savedTheme)) {
      setCurrentTheme(savedTheme)
    } else {
      // Default to system theme if no preference is saved
      setCurrentTheme("system")
    }
  }, [])

  // Apply theme changes to the document
  const applyTheme = (theme: Theme) => {
    const root = document.documentElement
    
    if (theme === "system") {
      // Remove any manually set theme classes and let CSS media queries handle it
      root.classList.remove("light", "dark")
      
      // Check system preference
      const systemPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      if (systemPrefersDark) {
        root.classList.add("dark")
      } else {
        root.classList.add("light")
      }
    } else {
      // Apply the selected theme
      root.classList.remove("light", "dark")
      root.classList.add(theme)
    }
  }

  const handleThemeChange = async (newTheme: Theme) => {
    setIsApplying(true)

    try {
      // Apply theme immediately for visual feedback
      applyTheme(newTheme)

      // Save to localStorage
      localStorage.setItem("theme", newTheme)

      // Update state
      setCurrentTheme(newTheme)

      // Try to save to backend preferences (only for light/dark, not system)
      if (newTheme !== "system") {
        try {
          await UserProfileAPI.updatePreferences({ theme: newTheme })
        } catch (error) {
          console.warn("Failed to save theme preference to backend:", error)
          // Don't show error to user, localStorage is sufficient
        }
      }

      // Show success message
      toast.success(`Theme changed to ${newTheme}`)

      // Small delay to show the applying state
      await new Promise(resolve => setTimeout(resolve, 300))

    } catch (error) {
      console.error("Failed to apply theme:", error)
      toast.error("Failed to apply theme changes")
    } finally {
      setIsApplying(false)
    }
  }

  // Listen for system theme changes when system theme is selected
  useEffect(() => {
    if (currentTheme === "system") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
      
      const handleSystemThemeChange = () => {
        applyTheme("system")
      }
      
      mediaQuery.addEventListener("change", handleSystemThemeChange)
      
      return () => {
        mediaQuery.removeEventListener("change", handleSystemThemeChange)
      }
    }
  }, [currentTheme])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Theme Preference</CardTitle>
          <CardDescription>
            Choose how the Medical AI interface appears to you
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={currentTheme}
            onValueChange={(value) => handleThemeChange(value as Theme)}
            disabled={isApplying}
            className="space-y-3"
          >
            {themeOptions.map((option) => (
              <div
                key={option.value}
                className={`flex items-center space-x-3 rounded-lg border p-4 transition-colors ${
                  currentTheme === option.value
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <RadioGroupItem
                  value={option.value}
                  id={option.value}
                  disabled={isApplying}
                />
                <div className="flex items-center space-x-3 flex-1">
                  <div className="flex-shrink-0 text-gray-600">
                    {option.icon}
                  </div>
                  <div className="flex-1">
                    <Label
                      htmlFor={option.value}
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      {option.label}
                    </Label>
                    <p className="text-xs text-gray-500 mt-1">
                      {option.description}
                    </p>
                  </div>
                  {currentTheme === option.value && (
                    <CheckCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                  )}
                </div>
              </div>
            ))}
          </RadioGroup>

          {isApplying && (
            <div className="mt-4 flex items-center justify-center text-sm text-gray-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              Applying theme changes...
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Theme Preview</CardTitle>
          <CardDescription>
            See how your selected theme affects the interface
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Preview Card */}
            <div className="border rounded-lg p-4 bg-background">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">AI</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-foreground">Medical AI Assistant</p>
                  <p className="text-xs text-muted-foreground">Ready to help with your consultation</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="bg-muted rounded p-2">
                  <p className="text-sm text-muted-foreground">
                    This is how text appears in your selected theme
                  </p>
                </div>
                <Button size="sm" className="w-full">
                  Sample Button
                </Button>
              </div>
            </div>

            <div className="text-xs text-gray-500">
              <p>
                <strong>Note:</strong> Theme changes apply immediately and are saved automatically.
                The theme will persist across browser sessions.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Display Settings</CardTitle>
          <CardDescription>
            Additional customization options for your interface
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Reduce Motion</Label>
                <p className="text-xs text-gray-500 mt-1">
                  Minimize animations and transitions
                </p>
              </div>
              <Button variant="outline" size="sm" disabled>
                Coming Soon
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">High Contrast</Label>
                <p className="text-xs text-gray-500 mt-1">
                  Increase contrast for better visibility
                </p>
              </div>
              <Button variant="outline" size="sm" disabled>
                Coming Soon
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
