"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Loader2, Send, Mic, MicOff, LogOut, ArrowLeft, ChevronDown, Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import ConversationSidebar from "@/components/conversation-sidebar"
import VoiceChatInterface from "@/components/voice-chat-interface"

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  created_at: string
  message_type: string
  voice_url?: string | null
}

export default function ChatPage() {
  const { id } = useParams()
  const router = useRouter()
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isFetching, setIsFetching] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [isVoiceMode, setIsVoiceMode] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [sidebarManuallyClosed, setSidebarManuallyClosed] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>({})
  const [isInitialLoad, setIsInitialLoad] = useState(true)
  const [showScrollButton, setShowScrollButton] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Get user info from localStorage
    const userEmail = localStorage.getItem("user_email") || "User"
    setCurrentUser({ email: userEmail })
  }, [])

  // Fetch messages for the conversation
  useEffect(() => {
    const fetchMessages = async () => {
      setIsFetching(true)
      try {
        const authToken = localStorage.getItem("access_token")
        if (!authToken) {
          console.error("No auth token found")
          return
        }

        const response = await fetch(`https://medbot-backend.fly.dev/api/v1/conversations/${id}/messages`, {
          headers: {
            "Authorization": `Bearer ${authToken}`,
            "X-API-Auth": `Bearer ${authToken}`
          }
        })

        if (!response.ok) {
          throw new Error("Failed to fetch messages")
        }

        const data = await response.json()
        setMessages(data)
      } catch (error) {
        console.error("Error fetching messages:", error)
      } finally {
        setIsFetching(false)
        setIsInitialLoad(false)
      }
    }

    if (id) {
      fetchMessages()
    }
  }, [id])

  // Scroll to bottom when messages change, but only after initial load
  useEffect(() => {
    // Only auto-scroll if:
    // 1. We're not in the initial loading state
    // 2. This is not the initial load of the conversation
    // 3. There are messages to scroll to
    if (!isFetching && !isInitialLoad && messages.length > 0) {
      // Use a small delay to ensure the DOM has updated
      const timeoutId = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
      }, 100)

      return () => clearTimeout(timeoutId)
    }
  }, [messages, isFetching, isInitialLoad])

  // Ensure sidebar stays open and is stable
  useEffect(() => {
    const handleResize = () => {
      // Only auto-open sidebar on desktop if user hasn't manually closed it
      if (window.innerWidth >= 1024 && !sidebarManuallyClosed) {
        setSidebarOpen(true)
      }
      // On mobile, respect the current state
    }

    // Set initial state based on screen size
    handleResize()

    // Add resize listener with throttling to prevent excessive calls
    let resizeTimeout: NodeJS.Timeout
    const throttledResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(handleResize, 100)
    }

    window.addEventListener('resize', throttledResize)
    return () => {
      window.removeEventListener('resize', throttledResize)
      clearTimeout(resizeTimeout)
    }
  }, [sidebarManuallyClosed])

  // Handle scroll detection to show/hide scroll to bottom button
  // Use throttling to prevent excessive state updates that might interfere with sidebar
  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100
      setShowScrollButton(!isNearBottom && messages.length > 0)
    }
  }, [messages.length])

  // Throttled scroll handler to improve performance
  const throttledHandleScroll = useCallback(() => {
    let timeoutId: NodeJS.Timeout
    return () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(handleScroll, 100)
    }
  }, [handleScroll])()

  // Function to scroll to bottom manually
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const newMessage: Partial<Message> = {
      role: "user",
      content: inputValue,
      message_type: "text"
    }

    setMessages(prev => [...prev, newMessage as Message])
    setInputValue("")
    setIsLoading(true)

    // Scroll to bottom when user sends a new message
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
    }, 100)

    try {
      const authToken = localStorage.getItem("access_token")
      if (!authToken) {
        console.error("No auth token found")
        return
      }

      const response = await fetch(`https://medbot-backend.fly.dev/api/v1/conversations/${id}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${authToken}`,
          "X-API-Auth": `Bearer ${authToken}`
        },
        body: JSON.stringify(newMessage)
      })

      if (!response.ok) {
        throw new Error("Failed to send message")
      }

      // Fetch updated messages to get the AI response
      const messagesResponse = await fetch(`https://medbot-backend.fly.dev/api/v1/conversations/${id}/messages`, {
        headers: {
          "Authorization": `Bearer ${authToken}`,
          "X-API-Auth": `Bearer ${authToken}`
        }
      })

      if (messagesResponse.ok) {
        const data = await messagesResponse.json()
        setMessages(data)
      }
    } catch (error) {
      console.error("Error sending message:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const toggleVoiceMode = () => {
    setIsVoiceMode(!isVoiceMode)
  }

  const handleLogout = () => {
    localStorage.removeItem("access_token")
    router.push("/login")
  }

  const handleConversationSelect = (selectedId: string) => {
    router.push(`/chat/${selectedId}`)
  }

  const handleConversationDeleted = (deletedId: string) => {
    // If the deleted conversation is the current one, redirect to home
    if (deletedId === id) {
      router.push('/')
    }
  }

  const handleReturnToMain = () => {
    // Navigate to the main page while preserving authentication state
    router.push("/")
  }

  const handleSidebarToggle = (open: boolean) => {
    setSidebarOpen(open)
    if (!open) {
      setSidebarManuallyClosed(true)
    } else {
      setSidebarManuallyClosed(false)
    }
  }

  return (
    <div className="flex h-screen bg-background overflow-hidden flex-no-gap">
      {/* Sidebar */}
      <ConversationSidebar
        currentUser={currentUser}
        onLogout={handleLogout}
        onNewConversation={handleReturnToMain}
        onConversationSelect={handleConversationSelect}
        onConversationDeleted={handleConversationDeleted}
        activeConversation={id as string}
        isOpen={sidebarOpen}
        onToggle={handleSidebarToggle}
        showCloseButton={true}
        showSettings={true}
        className="sidebar-stable sidebar-transition"
        newButtonText="New Consultation"
        newButtonIcon={<Plus className="h-4 w-4 mr-2" />}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 bg-background h-full">
        {/* Top Bar */}
        <div className="h-16 border-b border-border flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleReturnToMain}
                className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Return</span>
              </Button>
              <h1 className="text-lg font-semibold text-foreground">
                Medical Consultation
              </h1>
            </div>
          </div>
          <div className="flex items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="flex items-center gap-2"
            >
              <LogOut className="h-4 w-4" />
              <span>Logout</span>
            </Button>
          </div>
        </div>

        {/* Chat Content */}
        <div className="flex-1 flex flex-col h-screen overflow-y-auto">
          {isVoiceMode ? (
            <VoiceChatInterface />
          ) : (
            <>
              {/* Chat messages with improved scrolling */}
              <div className="flex-1 flex flex-col overflow-hidden relative chat-container">
                <div
                  ref={chatContainerRef}
                  className="flex-1 overflow-y-auto p-4 scroll-smooth chat-scrollbar"
                  onScroll={throttledHandleScroll}
                >
                  <div className="space-y-6 min-h-full flex flex-col">
                    {isFetching ? (
                      <div className="flex justify-center items-center flex-1 py-20">
                        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                      </div>
                    ) : messages.length === 0 ? (
                      <div className="flex justify-center items-center flex-1 py-20 text-muted-foreground">
                        No messages yet. Start a conversation!
                      </div>
                    ) : (
                      <div className="flex-1 space-y-6">
                        {messages.map((message, index) => (
                          <div
                            key={message.id || index}
                            className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                          >
                            <div
                              className={`max-w-[80%] rounded-lg p-4 ${
                                message.role === "user"
                                  ? "bg-blue-500 text-white"
                                  : "bg-muted text-foreground"
                              }`}
                            >
                              <div className="whitespace-pre-wrap break-words">{message.content}</div>
                              <div className={`text-xs mt-1 ${message.role === "user" ? "text-blue-100" : "text-muted-foreground"}`}>
                                {message.created_at && formatTime(message.created_at)}
                              </div>
                            </div>
                          </div>
                        ))}
                        {isLoading && (
                          <div className="flex justify-start">
                            <div className="bg-muted rounded-lg p-4 max-w-[80%]">
                              <div className="flex items-center space-x-2">
                                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                                <span className="text-muted-foreground">AI is thinking...</span>
                              </div>
                            </div>
                          </div>
                        )}
                        <div ref={messagesEndRef} />
                      </div>
                    )}
                  </div>
                </div>

                {/* Scroll to bottom button */}
                {showScrollButton && (
                  <Button
                    onClick={scrollToBottom}
                    className="absolute bottom-4 right-4 rounded-full w-10 h-10 p-0 bg-blue-600 hover:bg-blue-700 text-white shadow-lg"
                    size="sm"
                  >
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* Input area */}
              <div className="border-t p-4">
                <div className="flex items-end space-x-2">
                  <Textarea
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1 min-h-[60px] max-h-[200px]"
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault()
                        handleSendMessage()
                      }
                    }}
                    disabled={isLoading}
                  />
                  <div className="flex flex-col space-y-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={toggleVoiceMode}
                      className="h-16 w-16 rounded-full"
                    >
                      <Mic className="h-8 w-8 text-blue-500" />
                    </Button>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!inputValue.trim() || isLoading}
                      className="h-10"
                    >
                      <Send className="h-5 w-5" />
                    </Button>
                  </div>
                </div>
                <div className="text-xs text-center text-muted-foreground mt-2">
                  Medical AI can make mistakes. Check important info.
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
