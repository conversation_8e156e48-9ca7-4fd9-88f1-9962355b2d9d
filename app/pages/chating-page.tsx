"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  MessageSquare,
  ArrowLeft,
  Plus,
} from "lucide-react"
import VoiceChatInterface from "@/components/voice-chat-interface"
import ConversationSidebar from "@/components/conversation-sidebar"

interface ChatingPageProps {
  currentUser: any
  onLogout: () => void
  onReturn: () => void
  conversationId?: string | null
}

export default function ChatingPage({
  currentUser,
  onLogout,
  onReturn,
  conversationId
}: ChatingPageProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [activeConversation, setActiveConversation] = useState<string | null>(conversationId || null)

  const handleNewChat = () => {
    setActiveConversation(null)
    onReturn() // Return to main page when creating new chat
  }

  const handleConversationSelect = (id: string) => {
    setActiveConversation(id)
  }

  const handleConversationDeleted = (id: string) => {
    // If the deleted conversation was active, clear the active conversation
    if (activeConversation === id) {
      setActiveConversation(null)
    }
  }

  return (
    <div className="flex h-screen bg-white overflow-hidden flex-no-gap">
      {/* Sidebar */}
      <ConversationSidebar
        currentUser={currentUser}
        onLogout={onLogout}
        onNewConversation={handleNewChat}
        onConversationSelect={handleConversationSelect}
        onConversationDeleted={handleConversationDeleted}
        activeConversation={activeConversation}
        isOpen={sidebarOpen}
        onToggle={setSidebarOpen}
        showCloseButton={true}
        showSettings={true}
        className="sidebar-stable sidebar-transition"
        newButtonIcon={<Plus className="h-4 w-4 mr-2" />}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 bg-white h-full">
        {/* Top Bar */}
        <div className="h-16 border-b border-gray-200 flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onReturn}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Return</span>
              </Button>
              <h1 className="text-lg font-semibold text-gray-900">
                Medical Consultation
              </h1>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <MessageSquare className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Chat Content */}
        <div className="flex-1">
          <VoiceChatInterface conversationId={activeConversation} />
        </div>
      </div>
    </div>
  )
}
