"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { LogOut } from "lucide-react"
import <PERSON>Frame from "@/components/main-frame"
import ConversationSidebar from "@/components/conversation-sidebar"
import { useState, useCallback } from 'react'

interface StartingPageProps {
  currentUser: any
  onLogout: () => void
  onStartNewConversation: () => void | Promise<void>
}

export default function StartingPage({
  currentUser,
  onLogout,
  onStartNewConversation
}: StartingPageProps) {
  // Add state to track if navigation is in progress
  const [isNavigating, setIsNavigating] = useState(false);

  // Create a handler for conversation selection
  const handleConversationSelect = useCallback((conversationId: string) => {
    console.log("Conversation selected in StartingPage:", conversationId);
    
    // Prevent multiple rapid navigations
    if (isNavigating) {
      console.log("Navigation already in progress, ignoring");
      return;
    }
    
    if (conversationId) {
      setIsNavigating(true);
      console.log("Navigating to conversation:", conversationId);
      
      // IMPORTANT: Do NOT call onStartNewConversation here
      // Just navigate directly to the existing conversation
      window.location.href = `/chat/${conversationId}`;
      
      // Reset navigation flag after a delay (in case navigation fails)
      setTimeout(() => setIsNavigating(false), 2000);
    } else {
      // Only create a new conversation if no ID was provided
      console.log("No conversation ID provided, creating new conversation");
      onStartNewConversation();
    }
  }, [onStartNewConversation, isNavigating]);

  const handleConversationDeleted = useCallback((id: string) => {
    // No active conversation to handle on starting page
    console.log('Conversation deleted:', id);
  }, []);

  return (
    <div className="flex h-full bg-white">
      {/* Left Sidebar */}
      <ConversationSidebar
        currentUser={currentUser}
        onLogout={onLogout}
        onNewConversation={() => {
          console.log("New Consultation button clicked");
          onStartNewConversation();
        }}
        onConversationSelect={handleConversationSelect}
        onConversationDeleted={handleConversationDeleted}
        activeConversation={null}
        className="h-full"
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="h-16 border-b border-gray-200 flex items-center justify-between px-6">
          <h1 className="text-xl font-semibold text-gray-900">
            Medical AI Assistant
          </h1>
          <Button
            variant="outline"
            size="sm"
            onClick={onLogout}
            className="flex items-center gap-2"
          >
            <LogOut className="h-4 w-4" />
            <span>Logout</span>
          </Button>
        </div>

        {/* Main Content Area */}
        <div className="flex-1">
          <MainFrame onStartNewConversation={onStartNewConversation} />
        </div>
      </div>
    </div>
  )
}
