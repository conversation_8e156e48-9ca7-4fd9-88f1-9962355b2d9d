import type { Metada<PERSON> } from 'next'
import './globals.css'
import { Toaster } from 'sonner'
import ThemeProvider from '@/components/theme-provider'

export const metadata: Metadata = {
  title: 'Medical Chatbot',
  description: 'Created with v0',
  generator: 'v0.dev',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className="h-full overflow-y-auto h-screen">
        <ThemeProvider>
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
